import 'package:flutter/material.dart';

class AppTheme {
  static const Color primaryColor = Color(0xFFA68A64); // Soft brown
  static const Color secondaryColor = Color(0xFFD6CFC4); // Muted taupe
  static const Color accentColor = Color(0xFFA68A64); // Soft brown (same as primary)
  static const Color backgroundColor = Color(0xFFF8F5F0); // Light beige/cream
  static const Color textColor = Color(0xFF7C5C36); // Soft brown (WCAG AA compliant on beige)
  static const Color highlightOlive = Color(0xFFB6BFA4); // Muted olive green (for highlights, success)
  static const Color highlightRose = Color(0xFFE7B7B0); // Dusty rose (for highlights, info)
  static const Color highlightClay = Color(0xFFD8A48F); // Soft clay (for warnings, accents)
  static const Color errorColor = Color(0xFFC97A6A); // Muted clay red (error, alert)
  static const Color borderColor = Color(0xFFB8B2A7); // Muted taupe (borders, dividers)
  static const Color buttonHover = Color(0xFFBFAE9E); // Slightly darker beige for hover
  static const Color buttonActive = Color(0xFF927A5B); // Deeper brown for active
  static const Color iconHover = Color(0xFFB6BFA4); // Olive for icon hover
  static const Color iconActive = Color(0xFFA68A64); // Soft brown for icon active

  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: backgroundColor,
        onPrimary: Colors.white,
        onSecondary: textColor,
        onError: Colors.white,
        onSurface: textColor,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
        displayMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: textColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: textColor,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return buttonActive;
            } else if (states.contains(WidgetState.hovered)) {
              return buttonHover;
            }
            return primaryColor;
          }),
          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
            if (states.contains(WidgetState.hovered)) {
              return highlightOlive.withValues(alpha: 0.08);
            } else if (states.contains(WidgetState.pressed)) {
              return highlightClay.withValues(alpha: 0.12);
            }
            return null;
          }),
          padding: WidgetStateProperty.all<EdgeInsets>(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(color: borderColor),
            ),
          ),
        ),
      ),
      iconTheme: const IconThemeData(
        color: primaryColor,
        size: 24,
      ),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: const Color(0xFF23221F),
      colorScheme: ColorScheme.dark(
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: const Color(0xFF2C2B28),
        onPrimary: Colors.white,
        onSecondary: textColor,
        onError: Colors.white,
        onSurface: textColor,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Color(0xFFF8F5F0),
        ),
        displayMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Color(0xFFF8F5F0),
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Color(0xFFF8F5F0),
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Color(0xFFD6CFC4),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return buttonActive;
            } else if (states.contains(WidgetState.hovered)) {
              return buttonHover;
            }
            return primaryColor;
          }),
          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
          overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
            if (states.contains(WidgetState.hovered)) {
              return highlightOlive.withValues(alpha: 0.08);
            } else if (states.contains(WidgetState.pressed)) {
              return highlightClay.withValues(alpha: 0.12);
            }
            return null;
          }),
          padding: WidgetStateProperty.all<EdgeInsets>(
            const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(color: borderColor),
            ),
          ),
        ),
      ),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }
} 