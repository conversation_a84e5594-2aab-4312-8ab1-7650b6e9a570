import 'package:flutter/material.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/bottom_navigation_bar.dart';
import 'package:url_launcher/url_launcher.dart';

class SoapAnalysisPage extends StatefulWidget {
  const SoapAnalysisPage({super.key});

  @override
  State<SoapAnalysisPage> createState() => _SoapAnalysisPageState();
}

class _SoapAnalysisPageState extends State<SoapAnalysisPage> {
  bool showHighRiskOnly = false;

  void _onNavTap(int index) {
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/lab');
        break;
      case 2:
        _openOrderLink();
        break;
      case 3:
        _openLinks();
        break;
    }
  }

  void _openOrderLink() async {
    final url = Uri.parse('https://famistore.famiport.com.tw/users/3278142');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  void _openLinks() async {
    final url = Uri.parse('https://linktr.ee/hsslforms');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  final List<_IngredientInfo> allergens = [
    _IngredientInfo(
      name: 'Fragrance（香料）',
      function: '香味',
      reactions: '皮膚刺激、接觸性皮膚炎、氣喘誘發',
      notes: '即使是天然香料也可能過敏',
      highRisk: true,
    ),
    _IngredientInfo(
      name: 'Methylchloroisothiazolinone / Methylisothiazolinone (MIT/MI)',
      function: '防腐劑',
      reactions: '強烈過敏、紅疹、搔癢、濕疹',
      notes: '歐盟已禁用於免沖洗產品',
      highRisk: true,
    ),
    _IngredientInfo(
      name: 'Cocamidopropyl Betaine',
      function: '起泡',
      reactions: '接觸性皮膚炎',
      notes: '源自椰子油但可能含有副產物造成過敏',
      highRisk: false,
    ),
    _IngredientInfo(
      name: 'Phenoxyethanol',
      function: '防腐劑',
      reactions: '眼睛與皮膚刺激',
      notes: '相對溫和，但仍具風險',
      highRisk: false,
    ),
    _IngredientInfo(
      name: 'Benzyl Alcohol',
      function: '防腐劑 / 香料成分',
      reactions: '紅腫、癢感、微弱致敏性',
      notes: '',
      highRisk: false,
    ),
  ];

  final List<_EnvPersistentInfo> persistentIngredients = [
    _EnvPersistentInfo(
      name: 'Sodium Laureth Sulfate (SLES)',
      type: '合成界面活性劑',
      notes: '易造成水體泡沫堆積，有水生毒性',
    ),
    _EnvPersistentInfo(
      name: 'Cocamide Methyl MEA',
      type: '非離子界面活性劑',
      notes: '合成胺類，清潔效果好，但生物可分解性低',
    ),
    _EnvPersistentInfo(
      name: 'Polyquaternium-7',
      type: '聚合物',
      notes: '抗靜電增稠，但在自然中幾乎不可生物分解',
    ),
    _EnvPersistentInfo(
      name: 'Methylchloroisothiazolinone / Methylisothiazolinone',
      type: '防腐劑',
      notes: '難分解且具水生毒性',
    ),
    _EnvPersistentInfo(
      name: 'Disodium EDTA',
      type: '螯合劑',
      notes: '難被微生物分解，會造成環境中金屬離子累積',
    ),
  ];

  final String fullIngredientList =
      'Aqua, Lauric Acid, Sodium Laureth Sulfate, Myristic Acid, Potassium Hydroxide, Sodium Chloride, Cocamidopropyl Betaine, Fragrance, Cocamide Methyl MEA, Glycol Distearate, Glycerin, Phenoxyethanol, Disodium EDTA, Polyquaternium-7, Citric Acid, Benzyl Alcohol, Sodium Benzoate, Methylchloroisothiazolinone, Methylisothiazolinone, Terminalia Ferdinandiana Fruit Extract';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final cardColor = isDark ? Colors.grey[900] : Colors.brown[50];
    final warningColor = isDark ? Colors.red[200] : Colors.brown[300];
    final ecoColor = isDark ? Colors.amber[200] : Colors.green[200];

    return Scaffold(
      appBar: const CustomAppBar(title: 'Soap Analysis'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1: Introduction
            Text(
              '沐浴乳分析',
              style: theme.textTheme.displayMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '\n市售清潔劑成分解說（避免揭露廠牌）',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '大容量、便宜實惠、香氣撲鼻、包裝漂亮',
              style: theme.textTheme.bodyLarge,
            ),
            const SizedBox(height: 28),
            // Section 2: Allergens/Irritants
            Row(
              children: [
                Icon(Icons.warning, color: warningColor, size: 28),
                const SizedBox(width: 8),
                Text(
                  '潛在過敏或刺激性成分',
                  style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                FilterChip(
                  label: const Text('只顯示高風險'),
                  selected: showHighRiskOnly,
                  onSelected: (val) => setState(() => showHighRiskOnly = val),
                  selectedColor: warningColor,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...allergens.where((a) => !showHighRiskOnly || a.highRisk).map((a) => Card(
                  color: cardColor,
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  child: ListTile(
                    leading: Icon(Icons.warning_amber_rounded, color: warningColor),
                    title: Text(a.name, style: theme.textTheme.titleMedium),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('功能: ${a.function}'),
                        Text('反應: ${a.reactions}'),
                        if (a.notes.isNotEmpty) Text('備註: ${a.notes}'),
                      ],
                    ),
                  ),
                )),
            const SizedBox(height: 28),
            // Section 3: Environmentally Persistent Ingredients
            Row(
              children: [
                Icon(Icons.eco, color: ecoColor, size: 28),
                const SizedBox(width: 8),
                Text(
                  '可能不易分解的成分',
                  style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...persistentIngredients.map((e) => Card(
                  color: cardColor,
                  margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                  child: ListTile(
                    leading: Icon(Icons.eco_outlined, color: ecoColor),
                    title: Text(e.name, style: theme.textTheme.titleMedium),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('類別: ${e.type}'),
                        Text('備註: ${e.notes}'),
                      ],
                    ),
                  ),
                )),
            const SizedBox(height: 28),
            // Section 4: Full Ingredient List
            Row(
              children: [
                Icon(Icons.receipt_long, color: theme.colorScheme.primary, size: 28),
                const SizedBox(width: 8),
                Text(
                  '原始清單',
                  style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Card(
              color: cardColor,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: SelectableText(
                  fullIngredientList,
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: MainBottomNavigationBar(
        currentIndex: 1,
        onTap: _onNavTap,
      ),
    );
  }
}

class _IngredientInfo {
  final String name;
  final String function;
  final String reactions;
  final String notes;
  final bool highRisk;
  const _IngredientInfo({
    required this.name,
    required this.function,
    required this.reactions,
    required this.notes,
    required this.highRisk,
  });
}

class _EnvPersistentInfo {
  final String name;
  final String type;
  final String notes;
  const _EnvPersistentInfo({
    required this.name,
    required this.type,
    required this.notes,
  });
} 