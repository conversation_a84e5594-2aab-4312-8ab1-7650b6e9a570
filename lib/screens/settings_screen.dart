import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/custom_app_bar.dart';
import '../services/theme_provider.dart';
import '../services/language_provider.dart';
import '../l10n/app_localizations.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.settings,
        showSettingsButton: false,
      ),
      body: ListView(
        children: [
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return ListTile(
                leading: const Icon(Icons.dark_mode),
                title: Text(AppLocalizations.of(context)!.darkMode),
                trailing: Switch(
                  value: themeProvider.isDarkMode,
                  onChanged: (value) {
                    themeProvider.toggleTheme();
                  },
                ),
              );
            },
          ),
          Consumer<LanguageProvider>(
            builder: (context, languageProvider, child) {
              final currentLanguage = LanguageProvider.supportedLanguages.firstWhere(
                (lang) => lang['code'] == languageProvider.currentLocale.languageCode,
                orElse: () => {'code': 'en', 'name': AppLocalizations.of(context)!.english},
              );
              return ListTile(
                leading: const Icon(Icons.language),
                title: Text(AppLocalizations.of(context)!.language),
                subtitle: Text(currentLanguage['code'] == 'en'
                    ? AppLocalizations.of(context)!.english
                    : AppLocalizations.of(context)!.traditionalChinese),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  _showLanguageDialog(context, languageProvider);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.selectLanguage),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: LanguageProvider.supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = LanguageProvider.supportedLanguages[index];
                final isSelected = language['code'] == languageProvider.currentLocale.languageCode;
                return ListTile(
                  title: Text(language['code'] == 'en'
                      ? AppLocalizations.of(context)!.english
                      : AppLocalizations.of(context)!.traditionalChinese),
                  trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
                  onTap: () {
                    languageProvider.setLanguage(language['code']!);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
} 