// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'High School Soap Lab';

  @override
  String get settings => 'Settings';

  @override
  String get aboutUs => 'About Us';

  @override
  String get orderSoap => 'Order Soap';

  @override
  String get insideLab => 'Inside the Lab';

  @override
  String get supportUs => 'Support Us';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get traditionalChinese => 'Traditional Chinese';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get welcome => 'Welcome to \nHigh School Soap Lab';

  @override
  String get buySoap => 'Buy a Soap, Fund a Cause';

  @override
  String get buySoapDesc => 'Every purchase helps support our community initiatives';

  @override
  String get communityDesc => 'A community of high school students and young volunteers who come together to learn and teach soap-making skills.';

  @override
  String get findContact => 'Find all our contact information and social media links!';

  @override
  String get version => 'Version 1.0.0';
}
