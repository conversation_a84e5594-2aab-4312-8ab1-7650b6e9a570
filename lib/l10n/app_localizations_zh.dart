// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '高中生手工皂實驗室';

  @override
  String get settings => '設定';

  @override
  String get aboutUs => '關於我們';

  @override
  String get orderSoap => '訂購手工皂';

  @override
  String get insideLab => '實驗室內';

  @override
  String get supportUs => '支持我們';

  @override
  String get darkMode => '深色模式';

  @override
  String get language => '語言';

  @override
  String get english => '英文';

  @override
  String get traditionalChinese => '繁體中文';

  @override
  String get selectLanguage => '選擇語言';

  @override
  String get welcome => '歡迎來到\n高中生手工皂實驗室';

  @override
  String get buySoap => '買一塊皂，支持一個理念';

  @override
  String get buySoapDesc => '每一次購買都幫助我們支持社區計畫';

  @override
  String get communityDesc => '高中生與年輕志工組成的社群，一起學習並教導手工皂製作技術。';

  @override
  String get findContact => '點擊上方圖示取得我們的聯絡資訊與社群連結！';

  @override
  String get version => '版本 1.0.0';
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class AppLocalizationsZhHant extends AppLocalizationsZh {
  AppLocalizationsZhHant(): super('zh_Hant');

  @override
  String get appTitle => '高中生手工皂實驗室';

  @override
  String get settings => '設定';

  @override
  String get aboutUs => '關於我們';

  @override
  String get orderSoap => '訂購手工皂';

  @override
  String get insideLab => '實驗室內';

  @override
  String get supportUs => '支持我們';

  @override
  String get darkMode => '深色模式';

  @override
  String get language => '語言';

  @override
  String get english => '英文';

  @override
  String get traditionalChinese => '繁體中文';

  @override
  String get selectLanguage => '選擇語言';

  @override
  String get welcome => '歡迎來到\n高中生手工皂實驗室';

  @override
  String get buySoap => '買一塊皂，支持一個理念';

  @override
  String get buySoapDesc => '每一次購買都幫助我們支持社區計畫';

  @override
  String get communityDesc => '高中生與年輕志工組成的社群，一起學習並教導手工皂製作技術。';

  @override
  String get findContact => '點擊上方圖示取得我們的聯絡資訊與社群連結！';

  @override
  String get version => '版本 1.0.0';
}
