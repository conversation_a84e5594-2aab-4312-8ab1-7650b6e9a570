import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart';

/// Utility class for launching URLs
class UrlLauncherUtils {
  /// Launch a URL with error handling
  static Future<bool> launchURL(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri);
      } else {
        if (kDebugMode) {
          print('Could not launch $url');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error launching URL $url: $e');
      }
      return false;
    }
  }
  
  /// Launch URL with external application
  static Future<bool> launchURLExternal(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (kDebugMode) {
          print('Could not launch $url');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error launching URL $url: $e');
      }
      return false;
    }
  }
}
